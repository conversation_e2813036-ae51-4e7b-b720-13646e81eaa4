#!/usr/bin/env python3
"""
Quick demo of the bare bones two-stage depth estimation system
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2

from masknet import MaskNet, PatternReconstructionNet
from depthnet import DepthNet, TwoStageDepthEstimator

def quick_demo():
    """
    Quick demonstration of the two-stage system with random weights
    """
    print("=== Quick Demo: Two-Stage Depth Estimation ===")
    
    # Create a simple test image
    print("Creating synthetic test image...")
    img = np.zeros((256, 256, 3), dtype=np.uint8)
    
    # Add some color variation
    img[:, :, 0] = 150  # Red channel
    img[:, :, 1] = 100  # Green channel  
    img[:, :, 2] = 100  # Blue channel
    
    # Add a circular pattern
    center = 128
    radius = 80
    y, x = np.ogrid[:256, :256]
    mask = (x - center)**2 + (y - center)**2 <= radius**2
    img[mask] = [200, 150, 150]  # Brighter in center
    
    # Add structured light pattern
    x_coords = np.linspace(0, 4*np.pi, 256)
    pattern = (np.sin(x_coords) > 0).astype(np.float32)
    pattern_2d = np.tile(pattern, (256, 1))
    
    # Overlay pattern
    for c in range(3):
        img[:, :, c] = img[:, :, c] * 0.7 + pattern_2d * 255 * 0.3
    
    img = np.clip(img, 0, 255).astype(np.uint8)
    
    # Convert to tensor
    img_tensor = torch.FloatTensor(img / 255.0).permute(2, 0, 1).unsqueeze(0)
    
    print("Initializing networks...")
    
    # Method 1: Use individual networks
    print("\n--- Method 1: Individual Networks ---")
    mask_net = MaskNet(n_channels=3)
    pattern_net = PatternReconstructionNet(n_channels=3)
    depth_net = DepthNet(use_rgb=True)
    
    mask_net.eval()
    pattern_net.eval()
    depth_net.eval()
    
    with torch.no_grad():
        # Stage 1
        tissue_mask = mask_net(img_tensor)
        clean_pattern, pattern_confidence = pattern_net(img_tensor)
        
        # Stage 2
        depth_map = depth_net(clean_pattern, img_tensor)
        
        print(f"Tissue mask shape: {tissue_mask.shape}")
        print(f"Clean pattern shape: {clean_pattern.shape}")
        print(f"Pattern confidence shape: {pattern_confidence.shape}")
        print(f"Depth map shape: {depth_map.shape}")
    
    # Method 2: Use combined pipeline
    print("\n--- Method 2: Combined Pipeline ---")
    pipeline = TwoStageDepthEstimator(use_rgb_for_depth=True)
    pipeline.eval()
    
    with torch.no_grad():
        results = pipeline(img_tensor)
        
        print("Pipeline outputs:")
        for key, value in results.items():
            print(f"  {key}: {value.shape}")
    
    # Visualize results
    print("\nVisualizing results...")
    
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    # Top row: Individual network results
    axes[0, 0].imshow(img)
    axes[0, 0].set_title('Input Image')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(tissue_mask[0, 0].numpy(), cmap='gray')
    axes[0, 1].set_title('Tissue Mask')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(clean_pattern[0, 0].numpy(), cmap='gray')
    axes[0, 2].set_title('Clean Pattern')
    axes[0, 2].axis('off')
    
    axes[0, 3].imshow(depth_map[0, 0].numpy(), cmap='jet')
    axes[0, 3].set_title('Depth Map')
    axes[0, 3].axis('off')
    
    # Bottom row: Pipeline results
    axes[1, 0].imshow(img)
    axes[1, 0].set_title('Input Image')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(results['tissue_mask'][0, 0].numpy(), cmap='gray')
    axes[1, 1].set_title('Pipeline Mask')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(results['clean_pattern'][0, 0].numpy(), cmap='gray')
    axes[1, 2].set_title('Pipeline Pattern')
    axes[1, 2].axis('off')
    
    axes[1, 3].imshow(results['depth'][0, 0].numpy(), cmap='jet')
    axes[1, 3].set_title('Pipeline Depth')
    axes[1, 3].axis('off')
    
    plt.suptitle('Two-Stage Depth Estimation Demo')
    plt.tight_layout()
    plt.show()
    
    print("\nDemo complete!")
    print("\nNext steps:")
    print("1. Create test images: python create_test_image.py")
    print("2. Run full system: python main.py --image sample.jpg")
    print("3. Train on your image: python main.py --image your_image.jpg --train_iterations 100")

def test_model_sizes():
    """Test and report model sizes"""
    print("\n=== Model Size Information ===")
    
    mask_net = MaskNet(n_channels=3)
    pattern_net = PatternReconstructionNet(n_channels=3)
    depth_net = DepthNet(use_rgb=True)
    pipeline = TwoStageDepthEstimator(use_rgb_for_depth=True)
    
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"MaskNet parameters: {count_parameters(mask_net):,}")
    print(f"PatternReconstructionNet parameters: {count_parameters(pattern_net):,}")
    print(f"DepthNet parameters: {count_parameters(depth_net):,}")
    print(f"Complete Pipeline parameters: {count_parameters(pipeline):,}")
    
    # Test memory usage with different input sizes
    print("\n=== Memory Usage Test ===")
    test_sizes = [(256, 256), (512, 512), (640, 480)]
    
    for h, w in test_sizes:
        dummy_input = torch.randn(1, 3, h, w)
        
        try:
            with torch.no_grad():
                results = pipeline(dummy_input)
            print(f"✓ Input size {w}x{h}: Success")
        except Exception as e:
            print(f"✗ Input size {w}x{h}: {e}")

if __name__ == '__main__':
    quick_demo()
    test_model_sizes()
