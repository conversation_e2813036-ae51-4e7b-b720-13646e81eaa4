#!/usr/bin/env python3
"""
Bare Bones Two-Stage Structured Light Depth Estimation
Takes an image, produces tissue mask and depth map using two separate networks
"""

import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt
import argparse
import os

from masknet import MaskNet, PatternReconstructionNet
from depthnet import DepthNet, TwoStageDepthEstimator

def create_synthetic_training_data(image_path, depth_range=(10, 150)):
    """
    Create synthetic training data from a single image
    This simulates what would happen with real structured light
    """
    # Load and preprocess image
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"Could not load image: {image_path}")

    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_resized = cv2.resize(img_rgb, (256, 256))

    # Create synthetic structured light pattern
    h, w = img_resized.shape[:2]
    x = np.linspace(0, 4*np.pi, w)
    y = np.linspace(0, 4*np.pi, h)
    X, Y = np.meshgrid(x, y)

    # Binary fringe pattern (like what would be projected)
    pattern = (np.sin(X) > 0).astype(np.float32)

    # Create synthetic depth map (smooth variation)
    depth_synthetic = (50 + 30 * np.sin(X/2) * np.cos(Y/2) +
                      20 * np.sin(X) + 15 * np.cos(Y))
    depth_synthetic = np.clip(depth_synthetic, depth_range[0], depth_range[1])

    # Create tissue mask (assume center region is tissue)
    mask_synthetic = np.zeros((h, w), dtype=np.float32)
    center_h, center_w = h//2, w//2
    radius = min(h, w) // 3
    y_coords, x_coords = np.ogrid[:h, :w]
    mask_condition = (x_coords - center_w)**2 + (y_coords - center_h)**2 <= radius**2
    mask_synthetic[mask_condition] = 1.0

    # Simulate noisy observed pattern (what camera would see)
    noisy_pattern = pattern.copy()
    # Add some noise and distortion
    noisy_pattern += 0.1 * np.random.randn(h, w)
    noisy_pattern = np.clip(noisy_pattern, 0, 1)

    # Overlay pattern on image (simulate structured light projection)
    img_with_pattern = img_resized.copy().astype(np.float32) / 255.0
    img_with_pattern = img_with_pattern * 0.7 + np.stack([noisy_pattern]*3, axis=2) * 0.3
    img_with_pattern = np.clip(img_with_pattern, 0, 1)

    return {
        'rgb_image': img_with_pattern,
        'clean_pattern': pattern,
        'tissue_mask': mask_synthetic,
        'depth_map': depth_synthetic,
        'original_image': img_resized
    }

def train_on_single_image(image_path, num_iterations=100):
    """
    'Train' the networks on a single image by fitting to synthetic data
    This is a bare bones demonstration - not real training
    """
    print(f"Creating synthetic training data from {image_path}...")

    # Create synthetic data
    data = create_synthetic_training_data(image_path)

    # Convert to tensors
    rgb_tensor = torch.FloatTensor(data['rgb_image']).permute(2,0,1).unsqueeze(0)
    pattern_tensor = torch.FloatTensor(data['clean_pattern']).unsqueeze(0).unsqueeze(0)
    mask_tensor = torch.FloatTensor(data['tissue_mask']).unsqueeze(0).unsqueeze(0)
    depth_tensor = torch.FloatTensor(data['depth_map']).unsqueeze(0).unsqueeze(0)

    # Initialize networks
    print("Initializing networks...")
    mask_net = MaskNet(n_channels=3)
    pattern_net = PatternReconstructionNet(n_channels=3)
    depth_net = DepthNet(use_rgb=True)

    # Simple "training" - just a few gradient steps to fit this one example
    optimizer_mask = torch.optim.Adam(mask_net.parameters(), lr=0.01)
    optimizer_pattern = torch.optim.Adam(pattern_net.parameters(), lr=0.01)
    optimizer_depth = torch.optim.Adam(depth_net.parameters(), lr=0.01)

    print(f"'Training' for {num_iterations} iterations...")

    for i in range(num_iterations):
        # Train mask network
        optimizer_mask.zero_grad()
        pred_mask = mask_net(rgb_tensor)
        mask_loss = torch.nn.functional.binary_cross_entropy(pred_mask, mask_tensor)
        mask_loss.backward()
        optimizer_mask.step()

        # Train pattern network
        optimizer_pattern.zero_grad()
        pred_pattern, pred_confidence = pattern_net(rgb_tensor)
        pattern_loss = torch.nn.functional.mse_loss(pred_pattern, pattern_tensor)
        pattern_loss.backward()
        optimizer_pattern.step()

        # Train depth network
        optimizer_depth.zero_grad()
        pred_depth = depth_net(pattern_tensor, rgb_tensor)  # Use ground truth pattern for now
        depth_loss = torch.nn.functional.mse_loss(pred_depth, depth_tensor)
        depth_loss.backward()
        optimizer_depth.step()

        if (i + 1) % 20 == 0:
            print(f"Iteration {i+1}: Mask Loss: {mask_loss.item():.4f}, "
                  f"Pattern Loss: {pattern_loss.item():.4f}, "
                  f"Depth Loss: {depth_loss.item():.4f}")

    return mask_net, pattern_net, depth_net, data

def run_inference(image_path, mask_net, pattern_net, depth_net):
    """
    Run the complete two-stage inference pipeline
    """
    print(f"Running inference on {image_path}...")

    # Load and preprocess image
    img = cv2.imread(image_path)
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_resized = cv2.resize(img_rgb, (256, 256))
    img_tensor = torch.FloatTensor(img_resized / 255.0).permute(2,0,1).unsqueeze(0)

    # Set networks to evaluation mode
    mask_net.eval()
    pattern_net.eval()
    depth_net.eval()

    with torch.no_grad():
        # Stage 1: Get tissue mask and clean pattern
        tissue_mask = mask_net(img_tensor)
        clean_pattern, pattern_confidence = pattern_net(img_tensor)

        # Stage 2: Get depth from clean pattern
        depth_map = depth_net(clean_pattern, img_tensor)

        # Apply mask to depth (only show depth for tissue regions)
        masked_depth = depth_map * tissue_mask

    # Convert back to numpy for visualization
    results = {
        'original': img_resized,
        'tissue_mask': tissue_mask[0,0].numpy(),
        'clean_pattern': clean_pattern[0,0].numpy(),
        'pattern_confidence': pattern_confidence[0,0].numpy(),
        'raw_depth': depth_map[0,0].numpy(),
        'masked_depth': masked_depth[0,0].numpy()
    }

    return results

def visualize_results(results, synthetic_data=None):
    """
    Visualize all the results in a comprehensive plot
    """
    if synthetic_data is not None:
        # Show comparison with synthetic ground truth
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))

        # Top row: Ground truth
        axes[0,0].imshow(synthetic_data['original_image'])
        axes[0,0].set_title('Original Image')
        axes[0,0].axis('off')

        axes[0,1].imshow(synthetic_data['tissue_mask'], cmap='gray')
        axes[0,1].set_title('GT Tissue Mask')
        axes[0,1].axis('off')

        axes[0,2].imshow(synthetic_data['clean_pattern'], cmap='gray')
        axes[0,2].set_title('GT Clean Pattern')
        axes[0,2].axis('off')

        axes[0,3].imshow(synthetic_data['depth_map'], cmap='jet')
        axes[0,3].set_title('GT Depth Map')
        axes[0,3].axis('off')

        # Bottom row: Predictions
        axes[1,0].imshow(results['original'])
        axes[1,0].set_title('Input with Pattern')
        axes[1,0].axis('off')

        axes[1,1].imshow(results['tissue_mask'], cmap='gray')
        axes[1,1].set_title('Predicted Mask')
        axes[1,1].axis('off')

        axes[1,2].imshow(results['clean_pattern'], cmap='gray')
        axes[1,2].set_title('Predicted Pattern')
        axes[1,2].axis('off')

        axes[1,3].imshow(results['masked_depth'], cmap='jet')
        axes[1,3].set_title('Predicted Depth')
        axes[1,3].axis('off')

        plt.suptitle('Two-Stage Structured Light Depth Estimation Results')

    else:
        # Show just the results
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))

        axes[0,0].imshow(results['original'])
        axes[0,0].set_title('Original Image')
        axes[0,0].axis('off')

        axes[0,1].imshow(results['tissue_mask'], cmap='gray')
        axes[0,1].set_title('Tissue Mask')
        axes[0,1].axis('off')

        axes[0,2].imshow(results['clean_pattern'], cmap='gray')
        axes[0,2].set_title('Clean Pattern')
        axes[0,2].axis('off')

        axes[1,0].imshow(results['pattern_confidence'], cmap='hot')
        axes[1,0].set_title('Pattern Confidence')
        axes[1,0].axis('off')

        axes[1,1].imshow(results['raw_depth'], cmap='jet')
        axes[1,1].set_title('Raw Depth Map')
        axes[1,1].axis('off')

        axes[1,2].imshow(results['masked_depth'], cmap='jet')
        axes[1,2].set_title('Masked Depth Map')
        axes[1,2].axis('off')

        plt.suptitle('Two-Stage Depth Estimation Results')

    plt.tight_layout()
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='Bare Bones Two-Stage Depth Estimation')
    parser.add_argument('--image', type=str, default='sample.jpg',
                       help='Input image path')
    parser.add_argument('--train_iterations', type=int, default=100,
                       help='Number of training iterations')
    parser.add_argument('--no_train', action='store_true',
                       help='Skip training, use random weights')

    args = parser.parse_args()

    if not os.path.exists(args.image):
        print(f"Error: Image {args.image} not found!")
        print("Please provide a valid image file.")
        return

    print("=== Bare Bones Two-Stage Structured Light Depth Estimation ===")
    print(f"Input image: {args.image}")

    if not args.no_train:
        # "Train" on the single image
        mask_net, pattern_net, depth_net, synthetic_data = train_on_single_image(
            args.image, args.train_iterations
        )

        # Run inference
        results = run_inference(args.image, mask_net, pattern_net, depth_net)

        # Visualize with ground truth comparison
        visualize_results(results, synthetic_data)

    else:
        # Use random weights (no training)
        print("Using random weights (no training)...")
        mask_net = MaskNet(n_channels=3)
        pattern_net = PatternReconstructionNet(n_channels=3)
        depth_net = DepthNet(use_rgb=True)

        # Run inference
        results = run_inference(args.image, mask_net, pattern_net, depth_net)

        # Visualize results only
        visualize_results(results)

    print("\nDone! The system demonstrates:")
    print("1. Stage 1: Tissue segmentation + pattern reconstruction")
    print("2. Stage 2: Depth estimation from clean patterns")
    print("3. Combined pipeline for structured light depth estimation")

if __name__ == '__main__':
    main()
