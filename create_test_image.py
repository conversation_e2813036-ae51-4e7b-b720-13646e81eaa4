#!/usr/bin/env python3
"""
Create a test image for the bare bones depth estimation system
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt

def create_test_image(width=512, height=512):
    """
    Create a synthetic test image that simulates an endoscope view
    with structured light pattern
    """
    # Create base tissue-like background
    img = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Create tissue-like color (pinkish)
    img[:, :, 0] = 180  # Red
    img[:, :, 1] = 120  # Green
    img[:, :, 2] = 120  # Blue
    
    # Add some texture variation
    noise = np.random.randint(-30, 30, (height, width, 3))
    img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Create circular tissue region (simulating endoscope view)
    center_x, center_y = width // 2, height // 2
    radius = min(width, height) // 3
    
    y, x = np.ogrid[:height, :width]
    mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
    
    # Make background darker outside tissue region
    img[~mask] = img[~mask] // 3
    
    # Add structured light pattern (binary fringes)
    x_coords = np.linspace(0, 4*np.pi, width)
    y_coords = np.linspace(0, 4*np.pi, height)
    X, Y = np.meshgrid(x_coords, y_coords)
    
    # Create binary fringe pattern
    pattern = (np.sin(X) > 0).astype(np.float32)
    
    # Add some depth variation to the pattern (simulate 3D surface)
    depth_variation = 0.3 * np.sin(X/2) * np.cos(Y/2)
    pattern_with_depth = pattern + depth_variation
    pattern_with_depth = np.clip(pattern_with_depth, 0, 1)
    
    # Overlay pattern on image (simulate structured light projection)
    pattern_overlay = np.stack([pattern_with_depth] * 3, axis=2)
    img_with_pattern = img.astype(np.float32) / 255.0
    img_with_pattern = img_with_pattern * 0.7 + pattern_overlay * 0.3
    img_with_pattern = np.clip(img_with_pattern * 255, 0, 255).astype(np.uint8)
    
    return img_with_pattern

def create_multiple_test_images():
    """Create several test images with different characteristics"""
    
    # Test image 1: Basic circular tissue
    img1 = create_test_image(512, 512)
    cv2.imwrite('test_image_1.jpg', cv2.cvtColor(img1, cv2.COLOR_RGB2BGR))
    
    # Test image 2: Smaller size
    img2 = create_test_image(256, 256)
    cv2.imwrite('test_image_2.jpg', cv2.cvtColor(img2, cv2.COLOR_RGB2BGR))
    
    # Test image 3: Different aspect ratio
    img3 = create_test_image(640, 480)
    cv2.imwrite('test_image_3.jpg', cv2.cvtColor(img3, cv2.COLOR_RGB2BGR))
    
    # Create a simple sample.jpg for backward compatibility
    cv2.imwrite('sample.jpg', cv2.cvtColor(img1, cv2.COLOR_RGB2BGR))
    
    print("Created test images:")
    print("- test_image_1.jpg (512x512)")
    print("- test_image_2.jpg (256x256)")
    print("- test_image_3.jpg (640x480)")
    print("- sample.jpg (512x512, default)")
    
    # Show preview
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(img1)
    axes[0].set_title('Test Image 1 (512x512)')
    axes[0].axis('off')
    
    axes[1].imshow(img2)
    axes[1].set_title('Test Image 2 (256x256)')
    axes[1].axis('off')
    
    axes[2].imshow(img3)
    axes[2].set_title('Test Image 3 (640x480)')
    axes[2].axis('off')
    
    plt.suptitle('Generated Test Images with Structured Light Patterns')
    plt.tight_layout()
    plt.savefig('test_images_preview.png', dpi=150, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    print("Creating test images for structured light depth estimation...")
    create_multiple_test_images()
    print("\nTest images created successfully!")
    print("\nYou can now run the main system with:")
    print("python main.py --image sample.jpg")
    print("or")
    print("python main.py --image test_image_1.jpg --train_iterations 50")
